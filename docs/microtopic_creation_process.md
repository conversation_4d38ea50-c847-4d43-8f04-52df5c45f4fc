# 📝 Процесс добавления микротемы в системе

## 🎯 Обзор процесса

Система поддерживает два основных способа добавления микротем:
1. **Массовое добавление** через менеджер микротем (до 200 за раз)
2. **Добавление на лету** при создании домашних заданий

## 🔄 Способ 1: Массовое добавление через менеджер микротем

### Путь навигации:
```
Главное меню менеджера → Микротемы → Выбор предмета → Добавить микротемы
```

### Состояния FSM:
```python
class ManagerTopicStates(StatesGroup):
    main = State()              # Выбор предмета
    topics_list = State()       # Список микротем предмета  
    adding_topic = State()      # Ввод названий микротем
```

### Пошаговый процесс:

#### Шаг 1: Выбор предмета
- **Обработчик**: `show_subjects()`
- **Состояние**: `ManagerTopicStates.main`
- **Действие**: Показывает список всех предметов
- **UI**: Inline-клавиатура с предметами

#### Шаг 2: Просмотр существующих микротем
- **Обработчик**: `show_topics()`
- **Состояние**: `ManagerTopicStates.topics_list`
- **Действие**: Показывает список микротем с номерами
- **UI**: Кнопка "➕ Добавить микротемы"

#### Шаг 3: Ввод названий микротем
- **Обработчик**: `start_add_topic()`
- **Состояние**: `ManagerTopicStates.adding_topic`
- **Интерфейс**:
```
Предмет: Химия

📝 Введите названия микротем:
• Одну микротему на строку
• Можно ввести до 200 микротем за раз
• Пустые строки будут пропущены

Пример:
Алканы
Алкены
Алкины
```

#### Шаг 4: Обработка ввода
- **Обработчик**: `process_topic_name()`
- **Валидация**:
  - Максимум 200 микротем за раз
  - Уникальность названий в рамках предмета
  - Отсутствие дубликатов в текущем списке
  - Непустые названия

### Логика нумерации:
```python
# Автоматическое присвоение номеров
async def create_multiple(names: List[str], subject_id: int):
    # Получаем максимальный номер для предмета
    max_number = await get_max_number_for_subject(subject_id)
    next_number = (max_number or 0) + 1
    
    # Присваиваем последовательные номера
    for i, name in enumerate(names):
        microtopic.number = next_number + i
```

## 🔄 Способ 2: Добавление на лету при создании ДЗ

### Контекст:
При создании домашнего задания, если указанный номер микротемы не существует

### Пошаговый процесс:

#### Шаг 1: Запрос номера микротемы
- **Состояние**: `AddHomeworkStates.request_topic`
- **Интерфейс**: "Введите номер микротемы:"

#### Шаг 2: Валидация номера
- **Обработчик**: `process_topic()`
- **Проверки**:
  - Номер > 0
  - Существование предмета
  - Существование микротемы с данным номером

#### Шаг 3: Предложение создания (если не найдена)
- **Интерфейс**:
```
❌ Микротема с номером 5 не найдена для предмета 'Химия'.

📋 Доступные микротемы:
1. Органическая химия
2. Неорганическая химия  
3. Реакции

Выберите действие:
[➕ Создать микротему №5] [🔄 Ввести другой номер]
```

#### Шаг 4: Создание новой микротемы
- **Обработчик**: `handle_add_microtopic()`
- **Действие**: Сохраняет subject_id и topic_number
- **Состояние**: `AddHomeworkStates.add_microtopic_name`
- **Интерфейс**: "Введите название для микротемы №5:"

#### Шаг 5: Обработка названия
- **Обработчик**: `process_new_microtopic_name()`
- **Валидация**:
  - Непустое название
  - Уникальность в рамках предмета
- **Создание**: Автоматическое присвоение следующего номера

## 🔧 Техническая реализация

### Репозиторий микротем:
```python
class MicrotopicRepository:
    @staticmethod
    async def create(name: str, subject_id: int) -> Microtopic:
        # Одиночное создание с автонумерацией
        
    @staticmethod  
    async def create_multiple(names: List[str], subject_id: int) -> List[Microtopic]:
        # Массовое создание с последовательной нумерацией
        
    @staticmethod
    async def get_next_number_for_subject(subject_id: int) -> int:
        # Поиск следующего свободного номера
```

### Автоматическая нумерация:
- **Принцип**: Последовательная нумерация начиная с 1
- **При удалении**: Номера НЕ перенумеровываются (сохраняются пропуски)
- **При добавлении**: Новые микротемы получают следующий номер после максимального

### Валидация:
1. **Уникальность названия** в рамках предмета
2. **Существование предмета** перед созданием
3. **Лимиты**: До 200 микротем за одну операцию
4. **Очистка данных**: Удаление пустых строк и пробелов

## 📊 Примеры использования

### Массовое добавление:
```
Ввод:
Алканы
Алкены  
Алкины
Арены

Результат:
✅ Добавлено 4 микротемы в предмет Химия:
5. Алканы
6. Алкены  
7. Алкины
8. Арены
```

### Добавление на лету:
```
Контекст: Создание ДЗ, запрос микротемы №10

Если микротема №10 не существует:
1. Показ доступных микротем
2. Предложение создать №10
3. Ввод названия
4. Создание с автоматическим номером (например, 9)
5. Возврат к процессу создания ДЗ
```

## ⚠️ Важные особенности

1. **Номера НЕ переиспользуются** при удалении
2. **Автонумерация** всегда идет по возрастанию
3. **Уникальность** проверяется только в рамках одного предмета
4. **Транзакционность** - все микротемы создаются в одной транзакции
5. **Откат** при ошибке валидации любой из микротем

## 🎯 Результат

После успешного создания:
- Микротемы получают уникальные номера
- Доступны для использования в ДЗ
- Отображаются в списке микротем предмета
- Сохраняются в базе данных с привязкой к предмету
